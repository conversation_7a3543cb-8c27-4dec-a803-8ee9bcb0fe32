/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ControlPanel: typeof import('./../components/ControlPanel.vue')['default']
    DataCard: typeof import('./../components/DataCard.vue')['default']
    InfoPanel: typeof import('./../components/InfoPanel.vue')['default']
    MapContainer: typeof import('./../components/MapContainer.vue')['default']
    MapPanel: typeof import('./../components/MapPanel.vue')['default']
    PlayButton: typeof import('./../components/PlayButton.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TitleCard: typeof import('./../components/TitleCard.vue')['default']
  }
}
