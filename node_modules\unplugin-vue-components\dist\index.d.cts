import { ComponentInfo, ComponentResolveResult, ComponentResolver, ComponentResolverFunction, ComponentResolverObject, ComponentsImportMap, ImportInfo, ImportInfoLegacy, Matcher, Options, PublicPluginAPI, ResolvedOptions, SideEffectsInfo, SupportedTransformer, Transformer, TypeImport } from "./types-BED632qH.cjs";
import * as unplugin9 from "unplugin";
import { FilterPattern } from "unplugin-utils";

//#region src/core/unplugin.d.ts
declare const _default: unplugin9.UnpluginInstance<Options, boolean>;
//#endregion
//#region src/core/utils.d.ts
declare function pascalCase(str: string): string;
declare function camelCase(str: string): string;
declare function kebabCase(key: string): string;
//#endregion
export { ComponentInfo, ComponentResolveResult, ComponentResolver, ComponentResolverFunction, ComponentResolverObject, ComponentsImportMap, ImportInfo, ImportInfoLegacy, Matcher, Options, PublicPluginAPI, ResolvedOptions, SideEffectsInfo, SupportedTransformer, Transformer, TypeImport, camelCase, _default as default, kebabCase, pascalCase };