<template>
  <div class="title-card">
    <h1 class="title">{{ workoutType }}</h1>
    <div class="subtitle">
      <span class="date">{{ formatDate(workoutData?.startTime) }}</span>
      <span class="time">{{ formatTime(workoutData?.startTime) }}</span>
    </div>
    <div class="user-info" v-if="workoutData?.fullname">
      <span class="username">{{ workoutData.fullname }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DataService } from '@/services/DataService'

const dataService = DataService.getInstance()
const workoutData = ref<any>(null)

const workoutType = computed(() => {
  if (!workoutData.value) return '跑步'

  // 根据feedType或其他字段判断运动类型
  const feedType = workoutData.value.feedType
  if (feedType === 'WORKOUT') {
    return '跑步' // 可以根据更多数据判断具体类型
  }
  return '运动'
})

const formatDate = (timestamp: number | undefined) => {
  if (!timestamp) return '2024年1月15日'

  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatTime = (timestamp: number | undefined) => {
  if (!timestamp) return '08:30'

  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(async () => {
  try {
    const response = await dataService.loadWorkoutResponse()
    workoutData.value = response.workout
  } catch (error) {
    console.error('Failed to load workout data:', error)
  }
})
</script>

<style scoped>
.title-card {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #E0E0E0;
}

.title {
  font-size: 24px;
  color: #333333;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.subtitle {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.date, .time {
  font-size: 14px;
  color: #666666;
}

.user-info {
  margin-top: 8px;
}

.username {
  font-size: 12px;
  color: #999999;
  font-weight: 500;
}
</style>
