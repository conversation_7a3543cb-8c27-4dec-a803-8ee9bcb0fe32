<template>
  <div class="data-card">
    <div class="data-item" v-for="item in dataItems" :key="item.label">
      <div class="data-label">{{ item.label }}</div>
      <div class="data-value">{{ item.value }}</div>
      <div class="data-unit" v-if="item.unit">{{ item.unit }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DataService } from '@/services/DataService'

const dataService = DataService.getInstance()
const workoutData = ref<any>(null)

const dataItems = computed(() => {
  if (!workoutData.value) {
    return [
      { label: '持续时间', value: '--', unit: '' },
      { label: '距离', value: '--', unit: '' },
      { label: '一般配速', value: '--', unit: '' },
      { label: '平均心率', value: '--', unit: '' },
      { label: '最大心率', value: '--', unit: '' },
      { label: '最大摄氧量估计值', value: '--', unit: '' },
      { label: '能量消耗', value: '--', unit: '' },
      { label: '恢复时间', value: '--', unit: '' }
    ]
  }

  const data = workoutData.value

  return [
    {
      label: '持续时间',
      value: formatDuration(data.totalTime),
      unit: ''
    },
    {
      label: '距离',
      value: (data.totalDistance / 1000).toFixed(2),
      unit: 'km'
    },
    {
      label: '一般配速',
      value: formatPace(data.avgPace),
      unit: 'min/km'
    },
    {
      label: '平均心率',
      value: data.hrdata?.avg?.toString() || '--',
      unit: 'bpm'
    },
    {
      label: '最大心率',
      value: data.hrdata?.max?.toString() || '--',
      unit: 'bpm'
    },
    {
      label: '最大摄氧量估计值',
      value: getVO2Max(data),
      unit: 'ml/kg/min'
    },
    {
      label: '能量消耗',
      value: data.energyConsumption?.toString() || '--',
      unit: 'kcal'
    },
    {
      label: '恢复时间',
      value: formatRecoveryTime(data.recoveryTime),
      unit: 'h'
    }
  ]
})

const formatDuration = (totalTimeSeconds: number) => {
  if (!totalTimeSeconds) return '--'

  const minutes = Math.floor(totalTimeSeconds / 60)
  const seconds = Math.floor(totalTimeSeconds % 60)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

const formatPace = (avgPace: number) => {
  if (!avgPace) return '--'

  const minutes = Math.floor(avgPace)
  const seconds = Math.floor((avgPace - minutes) * 60)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

const getVO2Max = (data: any) => {
  // 从extensions中查找VO2Max数据
  if (data.extensions) {
    const fitnessExt = data.extensions.find((ext: any) => ext.type === 'FitnessExtension')
    if (fitnessExt && fitnessExt.estimatedVo2Max) {
      return fitnessExt.estimatedVo2Max.toFixed(1)
    }
  }
  return '--'
}

const formatRecoveryTime = (recoveryTimeSeconds: number) => {
  if (!recoveryTimeSeconds) return '--'

  const hours = Math.floor(recoveryTimeSeconds / 3600)
  return hours.toString()
}

onMounted(async () => {
  try {
    const response = await dataService.loadWorkoutResponse()
    workoutData.value = response.workout
  } catch (error) {
    console.error('Failed to load workout data:', error)
  }
})
</script>

<style scoped>
.data-card {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.data-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.data-item:last-child {
  border-bottom: none;
}

.data-label {
  font-size: 14px;
  color: #666666;
  font-weight: 400;
}

.data-value {
  font-size: 20px;
  color: #333333;
  font-weight: 600;
}

.data-unit {
  font-size: 12px;
  color: #999999;
  margin-top: -2px;
}
</style>
