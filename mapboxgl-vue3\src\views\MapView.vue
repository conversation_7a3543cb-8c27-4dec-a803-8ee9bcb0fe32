<template>
  <div class="page-container">
    <div class="main-content">
      <InfoPanel />
      <MapPanel />
    </div>
    <ControlPanel />
  </div>
</template>

<script setup lang="ts">
// AutoImport会自动导入Vue的API，组件会自动导入

onMounted(() => {
  console.log('MapView mounted with new layout')
})
</script>

<style scoped>
.page-container {
  width: 100vw;
  height: 100vh;
  background-color: #F5F5F5;
  position: relative;
  overflow: hidden;
}

.main-content {
  display: flex;
  width: 100%;
  height: 90vh;
  gap: 0;
  padding: 0;
  box-sizing: border-box;
}
</style>
