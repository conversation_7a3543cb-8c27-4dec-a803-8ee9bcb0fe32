{"name": "unplugin-auto-import", "type": "module", "version": "19.3.0", "description": "Register global imports on demand for Vite and Webpack", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unplugin/unplugin-auto-import#readme", "repository": {"type": "git", "url": "git+https://github.com/unplugin/unplugin-auto-import.git"}, "bugs": {"url": "https://github.com/unplugin/unplugin-auto-import/issues"}, "keywords": ["unplugin", "vite", "astro", "webpack", "rollup", "rspack", "auto-import", "transform"], "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./nuxt": {"import": {"types": "./dist/nuxt.d.ts", "default": "./dist/nuxt.js"}, "require": {"types": "./dist/nuxt.d.cts", "default": "./dist/nuxt.cjs"}}, "./astro": {"import": {"types": "./dist/astro.d.ts", "default": "./dist/astro.js"}, "require": {"types": "./dist/astro.d.cts", "default": "./dist/astro.cjs"}}, "./rollup": {"import": {"types": "./dist/rollup.d.ts", "default": "./dist/rollup.js"}, "require": {"types": "./dist/rollup.d.cts", "default": "./dist/rollup.cjs"}}, "./types": {"import": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "require": {"types": "./dist/types.d.cts", "default": "./dist/types.cjs"}}, "./vite": {"import": {"types": "./dist/vite.d.ts", "default": "./dist/vite.js"}, "require": {"types": "./dist/vite.d.cts", "default": "./dist/vite.cjs"}}, "./webpack": {"import": {"types": "./dist/webpack.d.ts", "default": "./dist/webpack.js"}, "require": {"types": "./dist/webpack.d.cts", "default": "./dist/webpack.cjs"}}, "./rspack": {"import": {"types": "./dist/rspack.d.ts", "default": "./dist/rspack.js"}, "require": {"types": "./dist/rspack.d.cts", "default": "./dist/rspack.cjs"}}, "./esbuild": {"import": {"types": "./dist/esbuild.d.ts", "default": "./dist/esbuild.js"}, "require": {"types": "./dist/esbuild.d.cts", "default": "./dist/esbuild.cjs"}}, "./*": "./*"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "files": ["*.d.ts", "dist"], "engines": {"node": ">=14"}, "peerDependencies": {"@nuxt/kit": "^3.2.2", "@vueuse/core": "*"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}, "@vueuse/core": {"optional": true}}, "dependencies": {"local-pkg": "^1.1.1", "magic-string": "^0.30.17", "picomatch": "^4.0.2", "unimport": "^4.2.0", "unplugin": "^2.3.4", "unplugin-utils": "^0.2.4"}, "devDependencies": {"@antfu/eslint-config": "^4.13.2", "@antfu/ni": "^24.4.0", "@antfu/utils": "^9.2.0", "@nuxt/kit": "^3.17.4", "@nuxt/schema": "^3.17.4", "@svgr/plugin-jsx": "^8.1.0", "@types/node": "^22.15.21", "@types/picomatch": "^3.0.2", "@types/resolve": "^1.20.6", "@vueuse/metadata": "^13.2.0", "bumpp": "^10.1.1", "eslint": "^9.27.0", "esno": "^4.8.0", "fast-glob": "^3.3.3", "publint": "^0.3.12", "rollup": "^4.41.1", "tsdown": "^0.12.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.1.4", "webpack": "^5.99.9"}, "scripts": {"build": "tsdown", "dev": "tsdown -w", "lint": "eslint .", "lint:fix": "nr lint --fix", "typecheck": "tsc", "play": "npm -C playground run dev", "release": "bumpp && pnpm publish", "start": "esno src/index.ts", "test": "vitest", "test:run": "vitest run"}}