{"name": "unimport", "type": "module", "version": "4.2.0", "description": "Unified utils for auto importing APIs in modules", "license": "MIT", "repository": "unjs/unimport", "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./unplugin": {"import": "./dist/unplugin.mjs", "require": "./dist/unplugin.cjs"}, "./addons": {"import": "./dist/addons.mjs", "require": "./dist/addons.cjs"}, "./*": "./*"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "engines": {"node": ">=18.12.0"}, "dependencies": {"acorn": "^8.14.1", "escape-string-regexp": "^5.0.0", "estree-walker": "^3.0.3", "local-pkg": "^1.1.1", "magic-string": "^0.30.17", "mlly": "^1.7.4", "pathe": "^2.0.3", "picomatch": "^4.0.2", "pkg-types": "^2.1.0", "scule": "^1.3.0", "strip-literal": "^3.0.0", "tinyglobby": "^0.2.12", "unplugin": "^2.2.2", "unplugin-utils": "^0.2.4"}, "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@types/estree": "^1.0.7", "@types/node": "^22.14.0", "@types/picomatch": "^4.0.0", "@vitest/coverage-v8": "^3.1.1", "bumpp": "^10.1.0", "conventional-changelog-cli": "^5.0.0", "eslint": "^9.24.0", "h3": "^1.15.1", "jquery": "^3.7.1", "lit": "^3.2.1", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}, "scripts": {"build": "unbuild", "dev": "vitest dev", "lint": "eslint .", "play": "pnpm -C playground run dev", "play:build": "pnpm -C playground run build", "typecheck": "vue-tsc --noEmit", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "release": "pnpm run test --run && bumpp -x \"pnpm run changelog\" --all && pnpm publish", "test": "vitest --coverage"}}